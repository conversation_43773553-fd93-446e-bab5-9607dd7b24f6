package services

import (
	"backend_go/config"
	"backend_go/models"
)

// GetStatusesService retrieves all statuses
func GetStatusesService() ([]models.Status, error) {
	var statuses []models.Status
	result := config.DB.Find(&statuses)
	return statuses, result.Error
}

// GetStatusByIDService retrieves a single status by ID
func GetStatusByIDService(id int) (models.Status, error) {
	var status models.Status
	result := config.DB.First(&status, id)
	return status, result.Error
}

// CreateStatusService creates a new status
func CreateStatusService(status *models.Status) error {
	result := config.DB.Create(status)
	return result.Error
}

// UpdateStatusService updates an existing status
func UpdateStatusService(status *models.Status) error {
	result := config.DB.Save(status)
	return result.Error
}

// DeleteStatusService deletes a status by ID
func DeleteStatusService(id int) (int64, error) {
	result := config.DB.Delete(&models.Status{}, id)
	return result.RowsAffected, result.Error
}
