package services

import (
	"backend_go/config"
	"backend_go/models"
)

// GetProjectsService retrieves all projects
func GetProjectsService() ([]models.Project, error) {
	var projects []models.Project
	result := config.DB.Find(&projects)
	return projects, result.Error
}

// GetProjectByIDService retrieves a single project by ID
func GetProjectByIDService(id int) (models.Project, error) {
	var project models.Project
	result := config.DB.First(&project, id)
	return project, result.Error
}

// CreateProjectService creates a new project
func CreateProjectService(project *models.Project) error {
	result := config.DB.Create(project)
	return result.Error
}

// UpdateProjectService updates an existing project
func UpdateProjectService(project *models.Project) error {
	result := config.DB.Save(project)
	return result.Error
}

// DeleteProjectService deletes a project by ID
func DeleteProjectService(id int) (int64, error) {
	result := config.DB.Delete(&models.Project{}, id)
	return result.RowsAffected, result.Error
}
