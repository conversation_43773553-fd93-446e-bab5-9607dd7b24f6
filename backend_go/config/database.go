package config

import (
	"backend_go/models"
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func ConnectDB() {
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		os.<PERSON>env("DB_HOST"),
		os.<PERSON>v("DB_USER"),
		os.<PERSON>("DB_PASSWORD"),
		os.<PERSON>v("DB_NAME"),
		os.<PERSON>env("DB_PORT"),
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}

	DB = db
	log.Println("✅ Connected to PostgreSQL")

	// Auto migrate the schema
	err = db.AutoMigrate(
		&models.User{},
		&models.Status{},
		&models.Project{},
		&models.Task{},
	)
	if err != nil {
		log.Fatal("❌ Failed to migrate database:", err)
	}
	log.Println("✅ Database migration completed")

	// Seed default data

}


