package config

import (
	"backend_go/models"
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func ConnectDB() *gorm.DB {
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		os.<PERSON>env("DB_HOST"),
		os.<PERSON>v("DB_USER"),
		os.<PERSON>v("DB_PASSWORD"),
		os.<PERSON>("DB_NAME"),
		os.<PERSON>env("DB_PORT"),
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}

	DB = db
	log.Println("✅ Connected to PostgreSQL")

	// Auto migrate โครงสร้างพื้นที่ก่อน
	err = db.AutoMigrate(
		&models.Province{},
		&models.District{},
		&models.Subdistrict{},
	)
	if err != nil {
		log.Fatal("❌ Failed to migrate area tables:", err)
	}

	// Auto migrate schema ที่เหลือ
	err = db.AutoMigrate(
		&models.User{},
		&models.Status{},
		&models.Court{},
		&models.CaseType{},
		&models.CaseColor{},
		&models.Creditor{},
		&models.Debtor{},
		&models.Employee{},
		&models.AssetType{},
		&models.AssetSta{},
		&models.Owner{},
		&models.Customer{},
		&models.AssetGroupSta{},
		&models.GroupType{},
		&models.AnnounceSta{},
		&models.Assessor{},
		&models.Case{},
		&models.Asset{},
		&models.AssetGroup{},
		&models.Announce{},
		&models.Event{},
		&models.AnnounceDetail{},
		&models.AnnounceDetailSta{},
		&models.Auction{},
		&models.Estimate{},
	)
	if err != nil {
		log.Fatal("❌ Failed to migrate database:", err)
	}
	log.Println("✅ Database migration completed")

	return db
}


