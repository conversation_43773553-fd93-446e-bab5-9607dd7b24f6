package main

import (
	"backend_go/config"
	"backend_go/models"
	"backend_go/routes"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Println("⚠️ Warning: .env file not found, using environment variables")
	}

	// Connect to database
	db := config.ConnectDB()

	// Seed data เมื่อ start server ครั้งแรก
	log.Println("🌱 เริ่มต้นสร้างข้อมูลตัวอย่าง...")
	if err := models.SeedData(db); err != nil {
		log.Printf("❌ เกิดข้อผิดพลาดในการสร้างข้อมูลตัวอย่าง: %v", err)
	} else {
		log.Println("✅ สร้างข้อมูลตัวอย่างเสร็จสิ้น!")
	}

	// Initialize Gin router
	router := gin.Default()

	// Setup routes
	routes.SetupRoutes(router)

	// Start server
	log.Println("🚀 Starting server on port 3002...")
	router.Run(":3002")
}
