services:
  # Go Backend - เชื่อมต่อกับ database ที่มีอยู่แล้ว
  cloudflared:
    image: cloudflare/cloudflared:latest
    restart: unless-stopped
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=eyJhIjoiYzdlNjdhZmJhNmVjNGNiZTgzOTU5MzdhZWVjNmIyYzMiLCJ0IjoiYjUzYThiOWItM2VlMC00NmQxLTlhYjAtYTgzOTZkMjE3NWQxIiwicyI6IllXVTVNakpqTTJJdE1tUXpZaTAwTmpabExXSTBZV0V0TldZME9URTFOekl6TXpFdyJ9
    networks:
      - tstack-network
  backend_go:
    build: .
    container_name: tstack-backend-go
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      - DB_HOST=tstack-db # ชื่อ container ของ database ที่มีอยู่
      - DB_USER=dev
      - DB_PASSWORD=dev
      - DB_NAME=dev
      - DB_PORT=5432
      - GIN_MODE=release
    networks:
      - tstack-network

networks:
  tstack-network:
    name: tstack-network
    external: true
