package routes

import (
	"backend_go/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterProjectRoutes(rg *gin.RouterGroup) {
	projects := rg.Group("/projects")
	{
		projects.GET("", controllers.GetProjects)
		projects.POST("", controllers.CreateProject)
		projects.GET(":id", controllers.GetProject)
		projects.PUT(":id", controllers.UpdateProject)
		projects.DELETE(":id", controllers.DeleteProject)
	}
}
