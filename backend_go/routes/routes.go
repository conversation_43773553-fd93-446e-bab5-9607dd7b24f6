package routes

import (
	"github.com/gin-gonic/gin"
)

func SetupRoutes(router *gin.Engine) {
	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Backend Go is running!",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		RegisterProjectRoutes(v1)
		// Users routes (placeholder)
		users := v1.Group("/users")
		{
			users.GET("", func(c *gin.Context) {
				c.<PERSON>SO<PERSON>(200, gin.H{"message": "Users endpoint - coming soon"})
			})
		}
		// Tasks routes (placeholder)
		tasks := v1.Group("/tasks")
		{
			tasks.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Tasks endpoint - coming soon"})
			})
		}
		RegisterStatusRoutes(v1)
	}
}