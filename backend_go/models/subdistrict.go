package models

type Subdistrict struct {
	SubdistrictId   int     `gorm:"primaryKey;column:subdistrictId"`
	SubdistrictName *string `gorm:"column:subdistrictName"`
	SubdistrictCode *string `gorm:"column:subdistrictCode"`
	DistrictId      *int    `gorm:"column:districtId"`

	// Relations
	District *District `gorm:"foreignKey:DistrictId"`
}

func (Subdistrict) TableName() string {
	return "subdistricts"
} 