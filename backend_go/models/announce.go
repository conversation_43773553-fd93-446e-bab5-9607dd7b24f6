package models

import "time"

type Announce struct {
	AnId          int        `gorm:"primaryKey;column:anId"`
	AnTitle       *string    `gorm:"column:anTitle"`
	AnDescription *string    `gorm:"column:anDescription"`
	CreatedAt     *time.Time `gorm:"column:CreatedAt"`
	AnStaId       *int       `gorm:"column:anStaId"`
	EmId          *int       `gorm:"column:em_id"`
	AnRemark      *string    `gorm:"column:anRemark"`
}

func (Announce) TableName() string {
	return "announces"
} 