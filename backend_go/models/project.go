package models

import (
	"time"

	"gorm.io/gorm"
)

// Project represents a project in the system
// Structure based on NestJS/Prisma schema
type Project struct {
	ID          int            `json:"id" gorm:"primaryKey;column:id"`
	Name        string         `json:"name" gorm:"column:name;not null"`
	Description *string        `json:"description" gorm:"column:description"`
	OwnerID     int            `json:"ownerId" gorm:"column:ownerId;not null"`
	Owner       User           `json:"owner" gorm:"foreignKey:OwnerID;references:id"`
	Tasks       []Task         `json:"tasks" gorm:"foreignKey:ProjectID"`
	CreatedAt   time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

func (Project) TableName() string {
	return "projects"
}