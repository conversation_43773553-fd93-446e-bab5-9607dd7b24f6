package models

import "time"

type AssetGroup struct {
	AssgId      int        `gorm:"primaryKey;column:assgId"`
	AssetId     *int       `gorm:"column:assetId"`
	GtId        *int       `gorm:"column:gtId"`
	CreatedAt   *time.Time `gorm:"column:CreatedAt"`
	AssgStaId   *int       `gorm:"column:assgStaId"`
	AssgDetail  *string    `gorm:"column:assgDetail"`
	AssgRemark  *string    `gorm:"column:assgRemark"`
}

func (AssetGroup) TableName() string {
	return "assetGroups"
} 