package models

import "time"

type Estimate struct {
	EsId        int        `gorm:"primaryKey;column:esId"`
	EsPrice     *float64   `gorm:"column:esPrice"`
	EsDate      *time.Time `gorm:"column:esDate"`
	AssessorId  *int       `gorm:"column:assessorId"`
	AssetId     *int       `gorm:"column:assetId"`
	CreatedAt   *time.Time `gorm:"column:CreatedAt"`
}

func (Estimate) TableName() string {
	return "estimates"
} 