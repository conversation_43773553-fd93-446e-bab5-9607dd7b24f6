package models

import "time"

type Asset struct {
	AssetId        int        `gorm:"primaryKey;column:assetId"`
	AssetName      *string    `gorm:"column:assetName"`
	AssetDetail    *string    `gorm:"column:assetDetail"`
	AssetLocation  *string    `gorm:"column:assetLocation"`
	AssetTypeId    *int       `gorm:"column:assetTypeId"`
	OwnerId        *int       `gorm:"column:ownerId"`
	CreatedAt      *time.Time `gorm:"column:CreatedAt"`
	CaseId         *int       `gorm:"column:caseId"`
	EmId           *int       `gorm:"column:em_id"`
	AssetStaId     *int       `gorm:"column:assetStaId"`
	SubdistrictId  *int       `gorm:"column:subdistrictId"`
	DistrictId     *int       `gorm:"column:districtId"`
	ProvinceId     *int       `gorm:"column:provinceId"`
	AssetRemark    *string    `gorm:"column:assetRemark"`
}

func (Asset) TableName() string {
	return "assets"
} 