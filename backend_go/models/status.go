package models

import (
	"time"

	"gorm.io/gorm"
)

// Status represents a task status
// Structure based on NestJS/Prisma schema
type Status struct {
	ID        int           `json:"id" gorm:"primaryKey;column:id"`
	Name      string        `json:"name" gorm:"column:name;uniqueIndex;not null"`
	Tasks     []Task        `json:"tasks" gorm:"foreignKey:StatusID"`
	CreatedAt time.Time     `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt time.Time     `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName overrides the table name used by Gorm to persist statuses
func (Status) TableName() string {
	return "status"
}
