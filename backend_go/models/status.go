package models

import (
	"time"

	"gorm.io/gorm"
)

type Status struct {
	ID        int           `json:"id" gorm:"primaryKey;column:id"`
	Name      string        `json:"name" gorm:"column:name;uniqueIndex;not null"`
	CreatedAt time.Time     `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt time.Time     `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}


func (Status) TableName() string {
	return "status"
}
