package models

import (
	"time"

	"gorm.io/gorm"
)

// Task represents a task in the system
// Structure based on NestJS/Prisma schema
type Task struct {
	ID          int            `json:"id" gorm:"primaryKey;column:id"`
	Name        string         `json:"name" gorm:"column:name;not null"`
	Description *string        `json:"description" gorm:"column:description"`
	StatusID    *int           `json:"statusId" gorm:"column:statusId"`
	Status      *Status        `json:"status" gorm:"foreignKey:StatusID;references:id"`
	ProjectID   int            `json:"projectId" gorm:"column:projectId;not null"`
	Project     Project        `json:"project" gorm:"foreignKey:ProjectID;references:id"`
	UserID      *int           `json:"userId" gorm:"column:userId"`
	User        *User          `json:"user" gorm:"foreignKey:UserID;references:id"`
	CreatedAt   time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

func (Task) TableName() string {
	return "tasks"
}
