package models

import (
	"log"

	"gorm.io/gorm"
)

// SeedData creates initial data for the database
func SeedData(db *gorm.DB) error {
	log.Println("🌱 Starting database seeding...")

	// Seed Provinces
	if err := seedProvinces(db); err != nil {
		return err
	}

	// Seed Asset Types
	if err := seedAssetTypes(db); err != nil {
		return err
	}

	// Seed Status Tables
	if err := seedStatusTables(db); err != nil {
		return err
	}

	log.Println("✅ Database seeding completed successfully!")
	return nil
}

func seedProvinces(db *gorm.DB) error {
	provinces := []Province{
		{ProvinceName: stringPtr("กรุงเทพมหานคร"), ProvinceCode: stringPtr("10")},
		{ProvinceName: stringPtr("เชียงใหม่"), ProvinceCode: stringPtr("50")},
		{ProvinceName: stringPtr("เชียงราย"), ProvinceCode: stringPtr("57")},
		{ProvinceName: stringPtr("นครราชสีมา"), ProvinceCode: stringPtr("30")},
		{ProvinceName: stringPtr("ขอนแก่น"), ProvinceCode: stringPtr("40")},
	}

	for _, province := range provinces {
		var existing Province
		if err := db.Where("province_code = ?", province.ProvinceCode).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&province).Error; err != nil {
				return err
			}
			log.Printf("Created province: %s", *province.ProvinceName)
		}
	}

	return nil
}

func seedAssetTypes(db *gorm.DB) error {
	assetTypes := []AssetType{
		{AssetTypeName: "ที่ดิน", AssetTypeCode: stringPtr("LAND")},
		{AssetTypeName: "บ้านเดี่ยว", AssetTypeCode: stringPtr("HOUSE")},
		{AssetTypeName: "คอนโดมิเนียม", AssetTypeCode: stringPtr("CONDO")},
		{AssetTypeName: "อาคารพาณิชย์", AssetTypeCode: stringPtr("COMMERCIAL")},
		{AssetTypeName: "โรงงาน", AssetTypeCode: stringPtr("FACTORY")},
	}

	for _, assetType := range assetTypes {
		var existing AssetType
		if err := db.Where("asset_type_code = ?", assetType.AssetTypeCode).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&assetType).Error; err != nil {
				return err
			}
			log.Printf("Created asset type: %s", assetType.AssetTypeName)
		}
	}

	return nil
}

func seedStatusTables(db *gorm.DB) error {
	// Seed Asset Status
	assetStatuses := []AssetSta{
		{AssetsStaName: stringPtr("พร้อมขาย"), AssetsStaCode: stringPtr("READY")},
		{AssetsStaName: stringPtr("ขายแล้ว"), AssetsStaCode: stringPtr("SOLD")},
		{AssetsStaName: stringPtr("ระงับการขาย"), AssetsStaCode: stringPtr("SUSPENDED")},
	}

	for _, status := range assetStatuses {
		var existing AssetSta
		if err := db.Where("assets_sta_code = ?", status.AssetsStaCode).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&status).Error; err != nil {
				return err
			}
			log.Printf("Created asset status: %s", *status.AssetsStaName)
		}
	}

	// Seed Auction Status
	auctionStatuses := []AuctionSta{
		{AuctionStaName: "รอการประมูล", AuctionStaCode: stringPtr("PENDING")},
		{AuctionStaName: "กำลังประมูล", AuctionStaCode: stringPtr("ACTIVE")},
		{AuctionStaName: "ประมูลสำเร็จ", AuctionStaCode: stringPtr("SUCCESS")},
		{AuctionStaName: "ประมูลไม่สำเร็จ", AuctionStaCode: stringPtr("FAILED")},
	}

	for _, status := range auctionStatuses {
		var existing AuctionSta
		if err := db.Where("auction_sta_code = ?", status.AuctionStaCode).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&status).Error; err != nil {
				return err
			}
			log.Printf("Created auction status: %s", status.AuctionStaName)
		}
	}

	// Seed Announce Status
	announceStatuses := []AnnounceSta{
		{AnStaName: "ร่าง", AnStaCode: stringPtr("DRAFT")},
		{AnStaName: "เผยแพร่", AnStaCode: stringPtr("PUBLISHED")},
		{AnStaName: "ปิดการประกาศ", AnStaCode: stringPtr("CLOSED")},
	}

	for _, status := range announceStatuses {
		var existing AnnounceSta
		if err := db.Where("an_sta_code = ?", status.AnStaCode).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&status).Error; err != nil {
				return err
			}
			log.Printf("Created announce status: %s", status.AnStaName)
		}
	}

	return nil
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
