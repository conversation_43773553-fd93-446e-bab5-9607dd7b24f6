package models

import (
	"time"

	"gorm.io/gorm"
)

type Auction struct {
	AuctionId    int            `json:"auctionId" gorm:"primaryKey;column:auctionId;autoIncrement"`
	AuctionStaId *int           `json:"auctionStaId" gorm:"column:auctionStaId"`
	AssgId       *int           `json:"assgId" gorm:"column:assgId"`
	EventId      *int           `json:"eventId" gorm:"column:eventId"`
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	EmId         *int           `json:"emId" gorm:"column:emId"`
	CusId        *int           `json:"cusId" gorm:"column:cusId"`
	AuctionPrice *float64       `json:"auctionPrice" gorm:"column:auctionPrice"`

	// Relations
	AuctionSta *AuctionSta `json:"auctionSta" gorm:"foreignKey:AuctionStaId"`
	AssetGroup *AssetGroup `json:"assetGroup" gorm:"foreignKey:AssgId"`
	Event      *Event      `json:"event" gorm:"foreignKey:EventId"`
	Employee   *Employee   `json:"employee" gorm:"foreignKey:EmId"`
	Customer   *Customer   `json:"customer" gorm:"foreignKey:CusId"`
}

func (Auction) TableName() string {
	return "auctions"
}
