package models

import "time"

type Auction struct {
	AuctionId      int        `gorm:"primaryKey;column:auctionId"`
	AuctionStaId   *int       `gorm:"column:auctionStaId"`
	AssgId         *int       `gorm:"column:assgId"`
	EventId        *int       `gorm:"column:eventId"`
	CreatedAt      *time.Time `gorm:"column:CreatedAt"`
	EmId           *int       `gorm:"column:emId"`
	CusId          *int       `gorm:"column:cusId"`
	AuctionPrice   *float64   `gorm:"column:auctionPrice"`

	// Relations
	AuctionSta *AuctionSta  `gorm:"foreignKey:AuctionStaId"`
	AssetGroup *AssetGroup  `gorm:"foreignKey:AssgId"`
	Event      *Event       `gorm:"foreignKey:EventId"`
	Employee   *Employee    `gorm:"foreignKey:EmId"`
	Customer   *Customer    `gorm:"foreignKey:CusId"`
}

func (Auction) TableName() string {
	return "auctions"
} 