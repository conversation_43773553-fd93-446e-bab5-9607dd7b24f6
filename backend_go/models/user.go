package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
// Structure based on NestJS/Prisma schema
type User struct {
	ID        int            `json:"id" gorm:"primaryKey;column:id"`
	Name      string         `json:"name" gorm:"column:name;not null"`
	Email     string         `json:"email" gorm:"column:email;uniqueIndex;not null"`
	Projects  []Project      `json:"projects" gorm:"foreignKey:OwnerID"`
	Tasks     []Task         `json:"tasks" gorm:"foreignKey:UserID"`
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName overrides the table name used by User to `users`
func (User) TableName() string {
	return "users"
}
